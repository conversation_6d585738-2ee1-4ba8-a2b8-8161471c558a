{% extends 'base.html' %}

{% block title %}تغيير كلمة المرور{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
        </h1>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-shield-alt me-2"></i>تغيير كلمة المرور
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="old_password" class="form-label">كلمة المرور الحالية</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="old_password" 
                                       name="old_password" required>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="togglePassword('old_password')">
                                    <i class="fas fa-eye" id="old_password_icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="new_password1" class="form-label">كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input type="password" class="form-control" id="new_password1" 
                                       name="new_password1" required>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="togglePassword('new_password1')">
                                    <i class="fas fa-eye" id="new_password1_icon"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل
                                </small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="new_password2" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-check"></i>
                                </span>
                                <input type="password" class="form-control" id="new_password2" 
                                       name="new_password2" required>
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="togglePassword('new_password2')">
                                    <i class="fas fa-eye" id="new_password2_icon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>نصائح لكلمة مرور قوية:</strong>
                            <ul class="mb-0 mt-2">
                                <li>استخدم 8 أحرف على الأقل</li>
                                <li>امزج بين الأحرف الكبيرة والصغيرة</li>
                                <li>أضف أرقام ورموز خاصة</li>
                                <li>تجنب المعلومات الشخصية</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الأمان</h6>
                </div>
                <div class="card-body">
                    <div class="security-info">
                        <div class="mb-3">
                            <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                            <h6>حماية حسابك</h6>
                            <p class="text-muted small">
                                تغيير كلمة المرور بانتظام يحمي حسابك من الوصول غير المصرح به
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <i class="fas fa-clock fa-2x text-success mb-2"></i>
                            <h6>آخر تسجيل دخول</h6>
                            <p class="text-muted small">
                                {{ user.last_login|date:"Y-m-d H:i" }}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                            <h6>تاريخ إنشاء الحساب</h6>
                            <p class="text-muted small">
                                {{ user.date_joined|date:"Y-m-d" }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المستخدم</h6>
                </div>
                <div class="card-body text-center">
                    <div class="user-avatar mb-3">
                        <i class="fas fa-user-circle fa-4x text-primary"></i>
                    </div>
                    <h5>{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-muted">{{ user.email|default:"لا يوجد بريد إلكتروني" }}</p>
                    <div class="badge bg-{% if user.is_superuser %}danger{% elif user.is_staff %}warning{% else %}primary{% endif %}">
                        {% if user.is_superuser %}
                            مدير النظام
                        {% elif user.is_staff %}
                            موظف
                        {% else %}
                            مستخدم
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// التحقق من تطابق كلمات المرور
document.getElementById('new_password2').addEventListener('input', function() {
    const password1 = document.getElementById('new_password1').value;
    const password2 = this.value;
    
    if (password2 && password1 !== password2) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});
</script>

<style>
.security-info i {
    display: block;
    margin: 0 auto;
}

.user-avatar {
    position: relative;
}

.input-group .btn {
    border-left: none;
}

.form-control:focus + .btn {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>
{% endblock %}
