@echo off
chcp 65001 > nul
title إعداد الوصول من أي مكان - محلات أبو علاء

echo.
echo ========================================
echo    🌐 إعداد الوصول من أي مكان
echo    Remote Access Setup
echo    محلات أبو علاء - Abu Alaa Stores
echo ========================================
echo.

cd /d "%~dp0"

echo 🔧 جاري إعداد النظام للوصول من الشبكة...
echo.

echo 📋 معلومات الشبكة الحالية:
python get_network_info.py ip

echo.
echo ✅ تم إعداد النظام بنجاح!
echo.
echo 🌐 يمكنك الآن الوصول للنظام من:
echo.
echo 📱 الهاتف المحمول:
echo    1. تأكد من اتصال الهاتف بنفس شبكة WiFi
echo    2. افتح المتصفح في الهاتف
echo    3. اذهب إلى: http://************:8000
echo.
echo 💻 أجهزة أخرى في الشبكة:
echo    استخدم نفس الرابط: http://************:8000
echo.
echo 🌍 من الإنترنت (يتطلب إعداد إضافي):
echo    http://**************:8000
echo    ⚠️ يحتاج إعداد Port Forwarding في الراوتر
echo.
echo 🔧 لبدء الخادم مع الوصول الشبكي:
echo    start_server_network.bat
echo.
echo 📖 للمزيد من التفاصيل:
echo    اقرأ ملف: REMOTE_ACCESS_GUIDE.md
echo.

pause
