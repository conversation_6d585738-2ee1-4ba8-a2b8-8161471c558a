{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}{{ customer.name }} - تفاصيل العميل - محلات أبو علاء{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        {{ customer.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'customer_edit' customer.id %}" class="btn btn-outline-primary">
                <i class="fas fa-edit me-1"></i>تعديل البيانات
            </a>
            <a href="{% url 'debt_create_for_customer' customer.id %}" class="btn btn-outline-success">
                <i class="fas fa-plus me-1"></i>إضافة دين
            </a>
        </div>
        <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات العميل -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">الاسم</label>
                    <p class="mb-0"><strong>{{ customer.name }}</strong></p>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">رقم الهاتف</label>
                    <p class="mb-0">
                        <i class="fas fa-phone me-1"></i>
                        <a href="tel:{{ customer.phone }}">{{ customer.phone }}</a>
                    </p>
                </div>
                
                {% if customer.email %}
                <div class="mb-3">
                    <label class="form-label text-muted">البريد الإلكتروني</label>
                    <p class="mb-0">
                        <i class="fas fa-envelope me-1"></i>
                        <a href="mailto:{{ customer.email }}">{{ customer.email }}</a>
                    </p>
                </div>
                {% endif %}
                
                {% if customer.address %}
                <div class="mb-3">
                    <label class="form-label text-muted">العنوان</label>
                    <p class="mb-0">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        {{ customer.address }}
                    </p>
                </div>
                {% endif %}

                {% if customer.page_number %}
                <div class="mb-3">
                    <label class="form-label text-muted">رقم الصفحة الورقية</label>
                    <p class="mb-0">
                        <i class="fas fa-file-alt me-1"></i>
                        {{ customer.page_number }}
                    </p>
                </div>
                {% endif %}
                
                <div class="mb-0">
                    <label class="form-label text-muted">تاريخ الإضافة</label>
                    <p class="mb-0">{{ customer.created_at|date:"Y-m-d H:i" }}</p>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات مالية -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>الإحصائيات المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1">{{ customer.total_debt|currency }}</h4>
                            <small class="text-muted">إجمالي الديون</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ customer.total_paid|currency }}</h4>
                        <small class="text-muted">إجمالي المدفوع</small>
                    </div>
                </div>

                <div class="text-center">
                    {% if customer.remaining_debt > 0 %}
                        <h3 class="text-warning mb-1">{{ customer.remaining_debt|currency }}</h3>
                        <small class="text-muted">المبلغ المتبقي</small>
                    {% else %}
                        <h3 class="text-success mb-1">مسدد بالكامل</h3>
                        <small class="text-muted">لا توجد ديون متبقية</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة الديون -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    ديون العميل ({{ debts.count }})
                </h5>
                <a href="{% url 'debt_create_for_customer' customer.id %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة دين جديد
                </a>
            </div>
            <div class="card-body">
                {% if debts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>المتبقي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for debt in debts %}
                            <tr>
                                <td>
                                    <strong>{{ debt.amount|currency }}</strong>
                                </td>
                                <td>{{ debt.description|truncatechars:50 }}</td>
                                <td>
                                    {{ debt.due_date|date:"Y-m-d" }}
                                    {% if debt.is_overdue %}
                                        <span class="badge bg-danger ms-1">متأخر</span>
                                    {% elif debt.is_due_soon %}
                                        <span class="badge bg-warning ms-1">مستحق قريباً</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if debt.status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                    {% elif debt.status == 'overdue' %}
                                        <span class="badge bg-danger">متأخر</span>
                                    {% elif debt.status == 'active' %}
                                        <span class="badge bg-primary">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ debt.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if debt.remaining_amount > 0 %}
                                        <span class="text-warning">{{ debt.remaining_amount|currency }}</span>
                                    {% else %}
                                        <span class="text-success">مسدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'debt_detail' debt.id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if debt.remaining_amount > 0 %}
                                        <a href="{% url 'payment_create' debt.id %}" class="btn btn-outline-success" title="تسجيل دفعة">
                                            <i class="fas fa-credit-card"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد ديون لهذا العميل</h5>
                    <p class="text-muted">ابدأ بإضافة دين جديد</p>
                    <a href="{% url 'debt_create_for_customer' customer.id %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة دين جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
