@echo off
chcp 65001 > nul
echo ========================================
echo    إعداد التشغيل التلقائي لخادم محلات أبو علاء
echo    Abu Alaa Stores Server Auto-Start Setup
echo ========================================
echo.

set "SERVER_PATH=%~dp0"
set "STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
set "SHORTCUT_NAME=Abu Alaa Server.lnk"

echo 📁 مسار الخادم: %SERVER_PATH%
echo 📁 مجلد البدء التلقائي: %STARTUP_FOLDER%
echo.

echo 🔧 إنشاء اختصار التشغيل التلقائي...

:: إنشاء ملف VBScript لإنشاء الاختصار
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%STARTUP_FOLDER%\%SHORTCUT_NAME%" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "python.exe" >> CreateShortcut.vbs
echo oLink.Arguments = """%SERVER_PATH%server_manager.py""" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%SERVER_PATH%" >> CreateShortcut.vbs
echo oLink.IconLocation = "python.exe" >> CreateShortcut.vbs
echo oLink.Description = "خادم نظام إدارة العملاء والديون - محلات أبو علاء" >> CreateShortcut.vbs
echo oLink.WindowStyle = 1 >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

:: تشغيل VBScript
cscript CreateShortcut.vbs > nul

:: حذف ملف VBScript المؤقت
del CreateShortcut.vbs

if exist "%STARTUP_FOLDER%\%SHORTCUT_NAME%" (
    echo ✅ تم إنشاء اختصار التشغيل التلقائي بنجاح!
    echo 📍 الموقع: %STARTUP_FOLDER%\%SHORTCUT_NAME%
    echo.
    echo 🎯 سيتم تشغيل الخادم تلقائياً عند بدء تشغيل Windows
    echo.
    echo 🔧 للتحكم في الخادم:
    echo    - تشغيل: python server_manager.py
    echo    - حالة: python server_manager.py status
    echo    - مساعدة: python server_manager.py help
    echo.
    echo 🗑️ لإزالة التشغيل التلقائي، احذف الملف:
    echo    %STARTUP_FOLDER%\%SHORTCUT_NAME%
) else (
    echo ❌ فشل في إنشاء اختصار التشغيل التلقائي
    echo 🔧 تأكد من صلاحيات الكتابة في مجلد البدء التلقائي
)

echo.
echo 📋 معلومات إضافية:
echo    - مجلد السجلات: %SERVER_PATH%logs\
echo    - ملف السجل: %SERVER_PATH%logs\server.log
echo    - عنوان الخادم: http://localhost:8000
echo.

pause
