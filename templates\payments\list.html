{% extends 'base.html' %}

{% block title %}قائمة الدفعات - محلات أبو علاء{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-credit-card me-2"></i>
        قائمة الدفعات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'debt_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-money-bill-wave me-1"></i>عرض الديون
        </a>
    </div>
</div>

<!-- الفلاتر والبحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="اسم العميل أو الملاحظات..." value="{{ search_query }}">
            </div>
            
            <div class="col-md-3">
                <label for="customer" class="form-label">العميل</label>
                <select class="form-select" id="customer" name="customer">
                    <option value="">جميع العملاء</option>
                    {% for customer in customers %}
                    <option value="{{ customer.id }}" {% if customer_filter == customer.id|stringformat:"s" %}selected{% endif %}>
                        {{ customer.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="method" class="form-label">طريقة الدفع</label>
                <select class="form-select" id="method" name="method">
                    <option value="">جميع الطرق</option>
                    {% for value, label in payment_methods %}
                    <option value="{{ value }}" {% if method_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
                <a href="{% url 'payment_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>مسح
                </a>
            </div>
        </form>
    </div>
</div>

<!-- قائمة الدفعات -->
<div class="card">
    <div class="card-body">
        {% if payments %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العميل</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>تاريخ الدفع</th>
                        <th>استلم بواسطة</th>
                        <th>الدين الأصلي</th>
                        <th>ملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>
                            <a href="{% url 'customer_detail' payment.debt.customer.id %}" class="text-decoration-none">
                                <strong>{{ payment.debt.customer.name }}</strong>
                            </a>
                            <br>
                            <small class="text-muted">{{ payment.debt.customer.phone }}</small>
                        </td>
                        <td>
                            <strong class="text-success">{{ payment.amount|floatformat:2 }} ريال</strong>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ payment.get_payment_method_display }}</span>
                        </td>
                        <td>
                            {{ payment.payment_date|date:"Y-m-d" }}
                            <br>
                            <small class="text-muted">{{ payment.payment_date|time:"H:i" }}</small>
                        </td>
                        <td>{{ payment.received_by.first_name|default:payment.received_by.username }}</td>
                        <td>
                            <a href="{% url 'debt_detail' payment.debt.id %}" class="text-decoration-none">
                                {{ payment.debt.amount|floatformat:2 }} ريال
                            </a>
                            <br>
                            <small class="text-muted">{{ payment.debt.description|truncatechars:30 }}</small>
                        </td>
                        <td>
                            {% if payment.notes %}
                                {{ payment.notes|truncatechars:40 }}
                            {% else %}
                                <span class="text-muted">لا توجد ملاحظات</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'debt_detail' payment.debt.id %}" 
                                   class="btn btn-outline-primary" title="عرض الدين">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'customer_detail' payment.debt.customer.id %}" 
                                   class="btn btn-outline-secondary" title="عرض العميل">
                                    <i class="fas fa-user"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- ترقيم الصفحات -->
        {% if payments.has_other_pages %}
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination justify-content-center">
                {% if payments.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ payments.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        صفحة {{ payments.number }} من {{ payments.paginator.num_pages }}
                    </span>
                </li>

                {% if payments.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ payments.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ payments.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if customer_filter %}&customer={{ customer_filter }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد دفعات</h5>
            {% if search_query or method_filter or customer_filter %}
                <p class="text-muted">لم يتم العثور على نتائج للفلاتر المحددة</p>
                <a href="{% url 'payment_list' %}" class="btn btn-outline-secondary">عرض جميع الدفعات</a>
            {% else %}
                <p class="text-muted">لا توجد دفعات مسجلة حتى الآن</p>
                <a href="{% url 'debt_list' %}" class="btn btn-primary">
                    <i class="fas fa-money-bill-wave me-1"></i>عرض الديون
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
