from django import template
from crm.models import CompanySettings

register = template.Library()

@register.simple_tag
def currency_symbol():
    """إرجاع رمز العملة من إعدادات الشركة"""
    try:
        settings = CompanySettings.objects.first()
        if settings:
            return settings.currency_symbol
    except CompanySettings.DoesNotExist:
        pass
    return 'د.ع'  # افتراضي

@register.simple_tag
def currency_name():
    """إرجاع اسم العملة من إعدادات الشركة"""
    try:
        settings = CompanySettings.objects.first()
        if settings:
            return settings.currency_name
    except CompanySettings.DoesNotExist:
        pass
    return 'دينار عراقي'  # افتراضي

@register.filter
def currency(value):
    """فلتر لإضافة رمز العملة للقيمة"""
    try:
        settings = CompanySettings.objects.first()
        symbol = settings.currency_symbol if settings else 'د.ع'
        return f"{value} {symbol}"
    except:
        return f"{value} د.ع"
