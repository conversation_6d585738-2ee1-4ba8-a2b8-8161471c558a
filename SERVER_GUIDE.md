# دليل تشغيل خادم محلات أبو علاء بشكل دائم
# Abu Alaa Stores Server Permanent Running Guide

## 📋 المحتويات
1. [الطرق المتاحة](#الطرق-المتاحة)
2. [الطريقة الأولى: مدير الخادم Python](#الطريقة-الأولى-مدير-الخادم-python)
3. [الطريقة الثانية: خدمة Windows](#الطريقة-الثانية-خدمة-windows)
4. [الطريقة الثالثة: التشغيل التلقائي](#الطريقة-الثالثة-التشغيل-التلقائي)
5. [المراقبة والصيانة](#المراقبة-والصيانة)
6. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🎯 الطرق المتاحة

### 1. **مدير الخادم Python** (الأسهل - مُوصى به)
- تشغيل دائم مع إعادة تشغيل تلقائي
- مراقبة مستمرة للخادم
- سجلات مفصلة

### 2. **خدمة Windows Service**
- تشغيل كخدمة نظام
- بدء تلقائي مع Windows
- إدارة من خلال خدمات Windows

### 3. **التشغيل التلقائي عند البدء**
- اختصار في مجلد البدء التلقائي
- بسيط وسهل الإعداد

---

## 🐍 الطريقة الأولى: مدير الخادم Python

### التشغيل:
```bash
# تشغيل الخادم مع المراقبة التلقائية
python server_manager.py

# عرض حالة الخادم
python server_manager.py status

# عرض المساعدة
python server_manager.py help
```

### المميزات:
- ✅ إعادة تشغيل تلقائي عند توقف الخادم
- ✅ مراقبة مستمرة للعمليات
- ✅ سجلات مفصلة في `logs/server.log`
- ✅ معالجة الأخطاء والاستثناءات
- ✅ إيقاف آمن بـ Ctrl+C

### الاستخدام:
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع: `cd Z:\crm`
3. شغل الأمر: `python server_manager.py`
4. الخادم سيعمل على: http://localhost:8000

---

## 🔧 الطريقة الثانية: خدمة Windows

### التثبيت:
```powershell
# تشغيل PowerShell كمدير
# ثم تنفيذ:
.\server_service.ps1 -Action install
```

### الإدارة:
```powershell
# بدء الخدمة
.\server_service.ps1 -Action start

# إيقاف الخدمة
.\server_service.ps1 -Action stop

# إعادة تشغيل الخدمة
.\server_service.ps1 -Action restart

# عرض حالة الخدمة
.\server_service.ps1 -Action status

# إلغاء تثبيت الخدمة
.\server_service.ps1 -Action uninstall
```

### المميزات:
- ✅ تشغيل كخدمة نظام
- ✅ بدء تلقائي مع Windows
- ✅ إدارة من خلال services.msc
- ✅ عمل في الخلفية بدون نوافذ

---

## 🚀 الطريقة الثالثة: التشغيل التلقائي

### الإعداد:
```bash
# تشغيل ملف الإعداد
setup_autostart.bat
```

### ما يحدث:
1. إنشاء اختصار في مجلد البدء التلقائي
2. تشغيل الخادم تلقائياً عند بدء Windows
3. استخدام `server_manager.py` للمراقبة

### الإزالة:
- احذف الاختصار من: `%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup`

---

## 📊 المراقبة والصيانة

### فحص حالة الخادم:
```bash
# فحص شامل
python server_manager.py status

# فحص المنفذ
netstat -an | findstr :8000

# فحص العمليات
tasklist | findstr python
```

### السجلات:
- **موقع السجلات:** `logs/server.log`
- **مستوى التسجيل:** INFO, WARNING, ERROR
- **التنظيف التلقائي:** كل 30 يوم

### النسخ الاحتياطي التلقائي:
```bash
# تشغيل النسخ الاحتياطي يدوياً
python manage.py auto_backup --type=local

# جدولة النسخ الاحتياطي (Task Scheduler)
# يومياً في الساعة 2:00 صباحاً
```

---

## 🔍 استكشاف الأخطاء

### المشاكل الشائعة:

#### 1. **المنفذ 8000 مستخدم**
```bash
# إيجاد العملية المستخدمة للمنفذ
netstat -ano | findstr :8000

# إنهاء العملية
taskkill /PID [رقم_العملية] /F
```

#### 2. **خطأ في قاعدة البيانات**
```bash
# تطبيق التحديثات
python manage.py migrate

# إنشاء مستخدم جديد
python manage.py createsuperuser
```

#### 3. **مشاكل الصلاحيات**
- تشغيل Command Prompt كمدير
- التأكد من صلاحيات الكتابة في مجلد المشروع

#### 4. **خطأ في Python**
```bash
# التحقق من إصدار Python
python --version

# تثبيت المتطلبات
pip install -r requirements.txt
```

### ملفات السجلات:
- **خادم Django:** `logs/server.log`
- **خدمة Windows:** Event Viewer > Windows Logs > Application
- **أخطاء Python:** في نافذة Command Prompt

---

## ⚙️ التكوين المتقدم

### ملف التكوين: `server_config.json`
```json
{
    "server": {
        "host": "0.0.0.0",
        "port": 8000,
        "auto_restart": true,
        "max_restarts": 10
    },
    "logging": {
        "level": "INFO",
        "file": "logs/server.log"
    }
}
```

### متغيرات البيئة:
```bash
# تعيين متغيرات البيئة
set DJANGO_SETTINGS_MODULE=abu_alaa_project.settings
set PYTHONPATH=Z:\crm
```

---

## 🌐 الوصول للخادم

### العناوين المتاحة:
- **محلي:** http://localhost:8000
- **الشبكة المحلية:** http://[IP_ADDRESS]:8000
- **جميع الواجهات:** http://0.0.0.0:8000

### فحص الاتصال:
```bash
# فحص محلي
curl http://localhost:8000

# فحص من جهاز آخر
ping [IP_ADDRESS]
telnet [IP_ADDRESS] 8000
```

---

## 📞 الدعم والمساعدة

### معلومات النظام:
- **المطور:** خالد شجاع
- **النظام:** نظام إدارة العملاء والديون
- **الإصدار:** 1.0.0
- **التاريخ:** 2025

### الملفات المهمة:
- `server_manager.py` - مدير الخادم الرئيسي
- `server_service.ps1` - إدارة خدمة Windows
- `setup_autostart.bat` - إعداد التشغيل التلقائي
- `server_config.json` - ملف التكوين
- `logs/server.log` - سجل الخادم

---

## 🎉 الخلاصة

**للتشغيل السريع:**
1. افتح Command Prompt
2. انتقل إلى مجلد المشروع: `cd Z:\crm`
3. شغل: `python server_manager.py`
4. افتح المتصفح: http://localhost:8000

**للتشغيل الدائم:**
1. شغل: `setup_autostart.bat`
2. أعد تشغيل الكمبيوتر
3. الخادم سيعمل تلقائياً

**للمراقبة:**
- `python server_manager.py status`
- فحص ملف `logs/server.log`

---

*تم تطوير هذا النظام بواسطة المبرمج خالد شجاع © 2025*
