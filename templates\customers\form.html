{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}
    {% if customer %}تعديل العميل - {{ customer.name }}{% else %}إضافة عميل جديد{% endif %} - محلات أبو علاء
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-{% if customer %}edit{% else %}plus{% endif %} me-2"></i>
        {% if customer %}تعديل العميل - {{ customer.name }}{% else %}إضافة عميل جديد{% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-1"></i>اسم العميل *
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{% if customer %}{{ customer.name }}{% endif %}" 
                                   required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>رقم الهاتف *
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{% if customer %}{{ customer.phone }}{% endif %}" 
                                   required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>البريد الإلكتروني
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="{% if customer %}{{ customer.email|default:'' }}{% endif %}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>العنوان
                        </label>
                        <textarea class="form-control" id="address" name="address" rows="3">{% if customer %}{{ customer.address|default:'' }}{% endif %}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="page_number" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>رقم الصفحة الورقية
                        </label>
                        <input type="text" class="form-control" id="page_number" name="page_number"
                               value="{% if customer %}{{ customer.page_number|default:'' }}{% endif %}"
                               placeholder="مثال: A-001">
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if customer %}تحديث البيانات{% else %}إضافة العميل{% endif %}
                        </button>
                        
                        <a href="{% url 'customer_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i>معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        الحقول المطلوبة مميزة بـ *
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-phone text-info me-2"></i>
                        رقم الهاتف يجب أن يكون فريد
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-envelope text-warning me-2"></i>
                        البريد الإلكتروني اختياري
                    </li>
                    <li>
                        <i class="fas fa-map-marker-alt text-secondary me-2"></i>
                        العنوان اختياري
                    </li>
                </ul>
            </div>
        </div>
        
        {% if customer %}
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-1"></i>إحصائيات العميل
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-1">{{ customer.debts.count }}</h5>
                            <small class="text-muted">عدد الديون</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1">{{ customer.total_debt|currency }}</h5>
                        <small class="text-muted">إجمالي الديون</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
